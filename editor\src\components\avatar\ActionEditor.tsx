/**
 * 动作编辑器组件
 * 用于编辑角色动作
 */
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select, Switch, Button, Table, Space, Tabs, Row, Col, Collapse, message } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, PlayCircleOutlined, StopOutlined, ImportOutlined, ExportOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';

// 定义本地动作类型
enum ActionType {
  BASIC = 'basic',
  COMBAT = 'combat',
  INTERACTION = 'interaction',
  EMOTE = 'emote',
  SPECIAL = 'special',
  COMBO = 'combo',
  ENVIRONMENT = 'environment',
  ITEM = 'item',
  SOCIAL = 'social',
  CUSTOM = 'custom'
}

enum ActionPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

interface ActionEvent {
  name: string;
  time: number;
  params?: any;
}

interface ActionData {
  id: string;
  name: string;
  type: ActionType;
  priority: ActionPriority;
  animationName: string;
  interruptible: boolean;
  loop: boolean;
  transitionTime: number;
  duration: number;
  events?: ActionEvent[];
}

const { Option } = Select;
const { TabPane } = Tabs;

interface ActionEditorProps {
  visible: boolean;
  entityId: string;
  onClose: () => void;
}

/**
 * 动作编辑器组件
 */
const ActionEditor: React.FC<ActionEditorProps> = ({ visible, entityId, onClose }) => {
  const { t } = useTranslation();
  
  // 获取实体数据
  const entity = useSelector((state: RootState) => 
    state.entities.entities.find(entity => entity.id === entityId)
  );
  
  // 动作列表
  const [actions, setActions] = useState<ActionData[]>([]);
  
  // 当前编辑的动作
  const [currentAction, setCurrentAction] = useState<ActionData | null>(null);
  
  // 当前编辑的事件
  const [currentEvent, setCurrentEvent] = useState<ActionEvent | null>(null);
  
  // 是否显示动作编辑对话框
  const [showActionModal, setShowActionModal] = useState(false);
  
  // 是否显示事件编辑对话框
  const [showEventModal, setShowEventModal] = useState(false);
  
  // 是否正在播放动作
  const [playingActionId, setPlayingActionId] = useState<string | null>(null);
  
  // 动作表单
  const [actionForm] = Form.useForm();
  
  // 事件表单
  const [eventForm] = Form.useForm();
  
  // 加载动作
  useEffect(() => {
    if (visible && entity) {
      // 模拟从实体加载动作
      // 实际实现应该从实体或动作控制系统获取动作数据
      const mockActions: ActionData[] = [
        {
          id: 'idle',
          name: '待机',
          type: ActionType.BASIC,
          priority: ActionPriority.LOW,
          animationName: 'idle',
          interruptible: true,
          loop: true,
          transitionTime: 0.3,
          duration: 0,
          events: []
        },
        {
          id: 'walk',
          name: '行走',
          type: ActionType.BASIC,
          priority: ActionPriority.LOW,
          animationName: 'walk',
          interruptible: true,
          loop: true,
          transitionTime: 0.3,
          duration: 0,
          events: []
        },
        {
          id: 'run',
          name: '跑步',
          type: ActionType.BASIC,
          priority: ActionPriority.LOW,
          animationName: 'run',
          interruptible: true,
          loop: true,
          transitionTime: 0.3,
          duration: 0,
          events: []
        },
        {
          id: 'jump',
          name: '跳跃',
          type: ActionType.BASIC,
          priority: ActionPriority.MEDIUM,
          animationName: 'jump',
          interruptible: false,
          loop: false,
          transitionTime: 0.2,
          duration: 1.0,
          events: [
            {
              name: 'jumpStart',
              time: 0.1,
              params: {
                effect: 'dust'
              }
            },
            {
              name: 'jumpApex',
              time: 0.5,
              params: {
                height: 1.0
              }
            }
          ]
        }
      ];
      
      setActions(mockActions);
    }
  }, [visible, entity]);
  
  // 添加动作
  const handleAddAction = () => {
    // 创建新动作
    const newAction: ActionData = {
      id: `action_${Date.now()}`,
      name: '新动作',
      type: ActionType.BASIC,
      priority: ActionPriority.LOW,
      animationName: '',
      interruptible: true,
      loop: false,
      transitionTime: 0.3,
      duration: 1.0,
      events: []
    };
    
    setCurrentAction(newAction);
    
    // 设置表单值
    actionForm.setFieldsValue({
      name: newAction.name,
      type: newAction.type,
      priority: newAction.priority,
      animationName: newAction.animationName,
      interruptible: newAction.interruptible,
      loop: newAction.loop,
      transitionTime: newAction.transitionTime,
      duration: newAction.duration
    });
    
    setShowActionModal(true);
  };
  
  // 编辑动作
  const handleEditAction = (action: ActionData) => {
    setCurrentAction(action);
    
    // 设置表单值
    actionForm.setFieldsValue({
      name: action.name,
      type: action.type,
      priority: action.priority,
      animationName: action.animationName,
      interruptible: action.interruptible,
      loop: action.loop,
      transitionTime: action.transitionTime,
      duration: action.duration
    });
    
    setShowActionModal(true);
  };
  
  // 删除动作
  const handleDeleteAction = (actionId: string) => {
    setActions(actions.filter(action => action.id !== actionId));
    
    if (playingActionId === actionId) {
      setPlayingActionId(null);
    }
  };
  
  // 播放动作
  const handlePlayAction = (actionId: string) => {
    // 这里应该调用动作控制系统播放动作
    // 实际实现需要与动作控制系统集成
    setPlayingActionId(actionId);
    
    // 模拟动作播放结束
    const action = actions.find(a => a.id === actionId);
    if (action && !action.loop && action.duration > 0) {
      setTimeout(() => {
        setPlayingActionId(null);
      }, action.duration * 1000);
    }
  };
  
  // 停止动作
  const handleStopAction = () => {
    // 这里应该调用动作控制系统停止动作
    // 实际实现需要与动作控制系统集成
    setPlayingActionId(null);
  };
  
  // 保存动作
  const handleSaveAction = () => {
    actionForm.validateFields().then(values => {
      if (!currentAction) return;
      
      // 更新动作
      const updatedAction: ActionData = {
        ...currentAction,
        name: values.name,
        type: values.type,
        priority: values.priority,
        animationName: values.animationName,
        interruptible: values.interruptible,
        loop: values.loop,
        transitionTime: values.transitionTime,
        duration: values.duration
      };
      
      // 更新动作列表
      const newActions = actions.map(action => 
        action.id === updatedAction.id ? updatedAction : action
      );
      
      if (!newActions.includes(updatedAction)) {
        newActions.push(updatedAction);
      }
      
      setActions(newActions);
      setShowActionModal(false);
      setCurrentAction(null);
    });
  };
  
  // 添加事件
  const handleAddEvent = () => {
    if (!currentAction) return;
    
    // 创建新事件
    const newEvent: ActionEvent = {
      name: '新事件',
      time: 0.0,
      params: {}
    };
    
    setCurrentEvent(newEvent);
    
    // 设置表单值
    eventForm.setFieldsValue({
      name: newEvent.name,
      time: newEvent.time,
      params: JSON.stringify(newEvent.params || {}, null, 2)
    });
    
    setShowEventModal(true);
  };
  
  // 编辑事件
  const handleEditEvent = (event: ActionEvent) => {
    setCurrentEvent(event);
    
    // 设置表单值
    eventForm.setFieldsValue({
      name: event.name,
      time: event.time,
      params: JSON.stringify(event.params || {}, null, 2)
    });
    
    setShowEventModal(true);
  };
  
  // 删除事件
  const handleDeleteEvent = (eventName: string, eventTime: number) => {
    if (!currentAction) return;
    
    // 更新当前动作的事件
    const updatedEvents = currentAction.events?.filter(
      event => !(event.name === eventName && event.time === eventTime)
    ) || [];
    
    // 更新当前动作
    const updatedAction = {
      ...currentAction,
      events: updatedEvents
    };
    
    setCurrentAction(updatedAction);
    
    // 更新动作列表
    const newActions = actions.map(action => 
      action.id === updatedAction.id ? updatedAction : action
    );
    
    setActions(newActions);
  };
  
  // 保存事件
  const handleSaveEvent = () => {
    eventForm.validateFields().then(values => {
      if (!currentAction || !currentEvent) return;
      
      let params = {};
      try {
        params = JSON.parse(values.params);
      } catch (error) {
        message.error(t('editor.avatar.invalidParamsJson'));
        return;
      }
      
      // 更新事件
      const updatedEvent: ActionEvent = {
        name: values.name,
        time: values.time,
        params
      };
      
      // 更新当前动作的事件
      const existingEventIndex = currentAction.events?.findIndex(
        event => event === currentEvent
      ) || -1;
      
      const updatedEvents = [...(currentAction.events || [])];
      
      if (existingEventIndex >= 0) {
        updatedEvents[existingEventIndex] = updatedEvent;
      } else {
        updatedEvents.push(updatedEvent);
      }
      
      // 更新当前动作
      const updatedAction = {
        ...currentAction,
        events: updatedEvents
      };
      
      setCurrentAction(updatedAction);
      
      // 更新动作列表
      const newActions = actions.map(action => 
        action.id === updatedAction.id ? updatedAction : action
      );
      
      setActions(newActions);
      setShowEventModal(false);
      setCurrentEvent(null);
    });
  };
  
  // 导出动作
  const handleExportActions = () => {
    // 创建JSON
    const json = JSON.stringify(actions, null, 2);
    
    // 创建下载链接
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `character_actions_${entityId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(t('editor.common.exportSuccess'));
  };
  
  // 导入动作
  const handleImportActions = () => {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const importedActions = JSON.parse(event.target?.result as string);
          
          // 验证导入的动作
          if (!Array.isArray(importedActions)) {
            throw new Error('Invalid actions format');
          }
          
          setActions(importedActions);
          message.success(t('editor.common.importSuccess'));
        } catch (error) {
          message.error(t('editor.common.importError'));
          console.error('导入动作失败:', error);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };
  
  // 动作表格列
  const actionColumns = [
    {
      title: t('editor.common.name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('editor.avatar.actionType'),
      dataIndex: 'type',
      key: 'type',
      render: (type: ActionType) => t(`editor.avatar.actionType_${type}`)
    },
    {
      title: t('editor.avatar.priority'),
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: ActionPriority) => t(`editor.avatar.priority_${priority}`)
    },
    {
      title: t('editor.avatar.animation'),
      dataIndex: 'animationName',
      key: 'animationName'
    },
    {
      title: t('editor.common.actions'),
      key: 'actions',
      render: (_: any, record: ActionData) => (
        <Space>
          {playingActionId === record.id ? (
            <Button 
              type="text" 
              icon={<StopOutlined />} 
              onClick={() => handleStopAction()}
            />
          ) : (
            <Button 
              type="text" 
              icon={<PlayCircleOutlined />} 
              onClick={() => handlePlayAction(record.id)}
            />
          )}
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditAction(record)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteAction(record.id)}
          />
        </Space>
      )
    }
  ];
  
  // 事件表格列
  const eventColumns = [
    {
      title: t('editor.common.name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('editor.avatar.eventTime'),
      dataIndex: 'time',
      key: 'time'
    },
    {
      title: t('editor.avatar.parameters'),
      dataIndex: 'params',
      key: 'params',
      render: (params: any) => (
        <div style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {JSON.stringify(params)}
        </div>
      )
    },
    {
      title: t('editor.common.actions'),
      key: 'actions',
      render: (_: any, record: ActionEvent) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditEvent(record)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteEvent(record.name, record.time)}
          />
        </Space>
      )
    }
  ];
  
  return (
    <Modal
      title={t('editor.avatar.actionEditor')}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="import" icon={<ImportOutlined />} onClick={handleImportActions}>
          {t('editor.common.import')}
        </Button>,
        <Button key="export" icon={<ExportOutlined />} onClick={handleExportActions}>
          {t('editor.common.export')}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('editor.common.close')}
        </Button>
      ]}
    >
      <div className="action-editor-container">
        <Tabs defaultActiveKey="actions">
          <TabPane tab={t('editor.avatar.actions')} key="actions">
            <div style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={handleAddAction}
              >
                {t('editor.avatar.addAction')}
              </Button>
            </div>
            
            <Table 
              dataSource={actions} 
              columns={actionColumns} 
              rowKey="id"
              pagination={false}
              onRow={(record) => ({
                onClick: () => handleEditAction(record)
              })}
            />
          </TabPane>
          
          <TabPane tab={t('editor.avatar.events')} key="events" disabled={!currentAction}>
            {currentAction && (
              <>
                <div style={{ marginBottom: 16 }}>
                  <h3>{t('editor.avatar.eventsForAction', { action: currentAction.name })}</h3>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />} 
                    onClick={handleAddEvent}
                  >
                    {t('editor.avatar.addEvent')}
                  </Button>
                </div>
                
                <Table 
                  dataSource={currentAction.events || []} 
                  columns={eventColumns} 
                  rowKey={(record) => `${record.name}_${record.time}`}
                  pagination={false}
                />
              </>
            )}
          </TabPane>
        </Tabs>
      </div>
      
      {/* 动作编辑对话框 */}
      <Modal
        title={currentAction?.id ? t('editor.avatar.editAction') : t('editor.avatar.addAction')}
        open={showActionModal}
        onCancel={() => setShowActionModal(false)}
        onOk={handleSaveAction}
      >
        <Form
          form={actionForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('editor.common.name')}
            rules={[{ required: true, message: t('editor.common.nameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="type"
            label={t('editor.avatar.actionType')}
            rules={[{ required: true, message: t('editor.avatar.actionTypeRequired') }]}
          >
            <Select>
              <Option value={ActionType.BASIC}>{t('editor.avatar.actionType_basic')}</Option>
              <Option value={ActionType.COMBO}>{t('editor.avatar.actionType_combo')}</Option>
              <Option value={ActionType.ENVIRONMENT}>{t('editor.avatar.actionType_environment')}</Option>
              <Option value={ActionType.ITEM}>{t('editor.avatar.actionType_item')}</Option>
              <Option value={ActionType.COMBAT}>{t('editor.avatar.actionType_combat')}</Option>
              <Option value={ActionType.SOCIAL}>{t('editor.avatar.actionType_social')}</Option>
              <Option value={ActionType.EMOTE}>{t('editor.avatar.actionType_emote')}</Option>
              <Option value={ActionType.CUSTOM}>{t('editor.avatar.actionType_custom')}</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="priority"
            label={t('editor.avatar.priority')}
            rules={[{ required: true, message: t('editor.avatar.priorityRequired') }]}
          >
            <Select>
              <Option value={ActionPriority.LOW}>{t('editor.avatar.priority_low')}</Option>
              <Option value={ActionPriority.MEDIUM}>{t('editor.avatar.priority_medium')}</Option>
              <Option value={ActionPriority.HIGH}>{t('editor.avatar.priority_high')}</Option>
              <Option value={ActionPriority.CRITICAL}>{t('editor.avatar.priority_critical')}</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="animationName"
            label={t('editor.avatar.animation')}
            rules={[{ required: true, message: t('editor.avatar.animationRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="interruptible"
                label={t('editor.avatar.interruptible')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="loop"
                label={t('editor.avatar.loop')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="transitionTime"
                label={t('editor.avatar.transitionTime')}
                rules={[{ type: 'number', min: 0 }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="duration"
                label={t('editor.avatar.duration')}
                rules={[{ type: 'number', min: 0 }]}
                tooltip={t('editor.avatar.durationTooltip')}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      
      {/* 事件编辑对话框 */}
      <Modal
        title={currentEvent ? t('editor.avatar.editEvent') : t('editor.avatar.addEvent')}
        open={showEventModal}
        onCancel={() => setShowEventModal(false)}
        onOk={handleSaveEvent}
      >
        <Form
          form={eventForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('editor.common.name')}
            rules={[{ required: true, message: t('editor.common.nameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="time"
            label={t('editor.avatar.eventTime')}
            rules={[{ required: true, type: 'number', min: 0, message: t('editor.avatar.eventTimeRequired') }]}
          >
            <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="params"
            label={t('editor.avatar.parameters')}
            tooltip={t('editor.avatar.parametersTooltip')}
          >
            <Input.TextArea rows={6} />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default ActionEditor;
